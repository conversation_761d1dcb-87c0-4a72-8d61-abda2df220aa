<template>
  <div class="home">
    <div class="hero-section">
      <div class="container">
        <h1 class="hero-title">📚 书籍可视化学习平台</h1>
        <p class="hero-subtitle">深度学习《The Well-Grounded Java Developer, Second Edition》</p>
        <div class="hero-features">
          <div class="feature">
            <span class="feature-icon">🎯</span>
            <span>互动式学习</span>
          </div>
          <div class="feature">
            <span class="feature-icon">📊</span>
            <span>可视化图表</span>
          </div>
          <div class="feature">
            <span class="feature-icon">💻</span>
            <span>代码实践</span>
          </div>
        </div>
        <RouterLink to="/books/well-grounded-java-developer" class="cta-button">
          查看书籍介绍 →
        </RouterLink>
      </div>
    </div>

    <div class="chapters-overview">
      <div class="container">
        <h2>章节概览</h2>
        <div class="chapters-grid">
          <div class="chapter-card" v-for="chapter in chapters" :key="chapter.id">
            <div class="chapter-number">{{ chapter.id }}</div>
            <h3>{{ chapter.title }}</h3>
            <p>{{ chapter.description }}</p>
            <RouterLink :to="chapter.path" class="chapter-link"> 学习本章 → </RouterLink>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const chapters = [
  {
    id: 1,
    title: 'Introducing Modern Java',
    description: '了解Java语言与平台的区别，新的发布模型，var关键字，以及Java 11的重要变更',
    path: '/chapter1',
  },
  {
    id: 2,
    title: 'Java Modules (JPMS)',
    description: '深入理解Java平台模块系统，掌握模块化架构设计和jlink工具的使用',
    path: '/chapter2',
  },
  {
    id: 3,
    title: 'Java 17 现代特性',
    description: '探索文本块、Switch表达式、Records、密封类型与模式匹配等现代Java特性',
    path: '/chapter3',
  },
  {
    id: 4,
    title: 'Class Files & Bytecode',
    description: '深入JVM底层：类加载机制、字节码指令与反射原理',
    path: '/chapter4',
  },
  {
    id: 5,
    title: 'Java Concurrency Fundamentals',
    description: '深入并发编程：从理论基础到字节码实现',
    path: '/chapter5',
  },
  {
    id: 6,
    title: 'JDK Concurrency Libraries',
    description: '从原子操作到异步编程：现代并发工具全景',
    path: '/chapter6',
  },
  {
    id: 7,
    title: 'Understanding Java Performance',
    description: '理解Java性能：从度量到优化的科学方法论，掌握GC、JIT与现代性能分析工具',
    path: '/chapter7',
  },
  {
    id: 8,
    title: 'Alternative JVM Languages',
    description: '探索JVM多语言编程：Kotlin、Clojure、Groovy等语言的特性与应用',
    path: '/chapter8',
  },
  {
    id: 9,
    title: 'Kotlin',
    description: '现代化JVM语言：简洁、安全、互操作的Java最佳伙伴',
    path: '/chapter9',
  },
  {
    id: 10,
    title: 'Clojure',
    description: '函数式编程精髓：不可变性、REPL驱动开发与代码即数据',
    path: '/chapter10',
  },
]
</script>

<style scoped>
.home {
  min-height: 100vh;
}

.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4rem 0;
  text-align: center;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.hero-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.hero-features {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  backdrop-filter: blur(10px);
}

.feature-icon {
  font-size: 1.2rem;
}

.cta-button {
  display: inline-block;
  background: white;
  color: #667eea;
  padding: 1rem 2rem;
  border-radius: 30px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.chapters-overview {
  padding: 4rem 0;
  background: #f8f9fa;
}

.chapters-overview h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #333;
}

.chapters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.chapter-card {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.chapter-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.chapter-number {
  position: absolute;
  top: -10px;
  right: -10px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
}

.chapter-card h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: #333;
}

.chapter-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.chapter-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.chapter-link:hover {
  color: #764ba2;
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-features {
    flex-direction: column;
    align-items: center;
  }

  .chapters-grid {
    grid-template-columns: 1fr;
  }
}
</style>
